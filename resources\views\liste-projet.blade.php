    @extends($userTheme ?? 'theme')

@section('contenu')
<!-- Inclure Animate.css pour les animations -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
<div class="page-content">
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
                    <h4 class="mb-sm-0">
                        <i class="ri-folder-line text-primary me-2"></i>{{ __('message.Project Management_list') }}
                    </h4>
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">TaskFlow</a></li>
                            <li class="breadcrumb-item active">{{ __('message.Projects_list') }}</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <!-- Alerts -->
        <div class="row">
            <div class="col-lg-12">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="ri-check-double-line me-1 align-middle"></i> {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="ri-error-warning-line me-1 align-middle"></i> {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card shadow-lg border-0 animate__animated animate__fadeIn">
                    <div class="card-header d-flex align-items-center bg-light">
                        <h5 class="card-title mb-0 flex-grow-1">{{ __('message.Project List_list') }}</h5>
                        <div class="flex-shrink-0">
                            <a href="{{ route('projet') }}" class="btn btn-primary animate__animated animate__pulse animate__infinite animate__slower">
                                <i class="ri-add-line align-bottom me-1"></i> {{ __('message.New Project_list') }}
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped align-middle mb-0 animate__animated animate__fadeIn">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">{{ __('message.Project_list') }}</th>
                                        <th scope="col">{{ __('message.Main Task_list') }}</th>
                                        <th scope="col">{{ __('message.Start Date_list') }}</th>
                                        <th scope="col">{{ __('message.End Date_list') }}</th>
                                        <th scope="col">{{ __('message.Progress_list') }}</th>
                                        <th scope="col" style="width: 150px;">{{ __('message.Actions_detail') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($projects as $project)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="flex-shrink-0 me-3">
                                                        @if($project->hasImage())
                                                            <div class="project-image-container">
                                                                <img src="{{ $project->getImageUrl('thumbnail') }}"
                                                                     alt="{{ $project->project_name }}"
                                                                     class="rounded project-thumbnail animate__animated animate__fadeIn"
                                                                     style="width: 60px; height: 60px; object-fit: cover; border: 2px solid #e9ecef;">
                                                            </div>
                                                        @else
                                                            <div class="avatar-sm animate__animated animate__fadeIn">
                                                                <span class="avatar-title bg-soft-primary text-primary rounded-circle fs-5">
                                                                    {{ strtoupper(substr($project->project_name, 0, 1)) }}
                                                                </span>
                                                            </div>
                                                        @endif
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <a href="{{ route('projet-detail', ['id' => $project->id]) }}" class="text-reset">
                                                            <h5 class="fs-14 mb-0">{{ $project->project_name }}</h5>
                                                        </a>
                                                        @if($project->task_description)
                                                            <p class="text-muted mb-2 small">{{ Str::limit($project->task_description, 50) }}</p>
                                                        @endif
                                                        <a href="{{ route('entreprise.dashboard') }}?projet_id={{ $project->id }}" class="btn btn-sm btn-soft-info mt-1 animate__animated animate__fadeIn">
                                                            <i class="ri-dashboard-line me-1"></i> Kanban Board
                                                        </a>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $project->task_title }}</td>
                                            <td>{{ \Carbon\Carbon::parse($project->start_date)->format('d/m/Y') }}</td>
                                            <td>{{ $project->end_date ? \Carbon\Carbon::parse($project->end_date)->format('d/m/Y') : 'Non définie' }}</td>
                                            <td>
                                                @php
                                                    $totalTaches = $project->taches->count();
                                                    $tachesTerminees = $project->taches->where('statut', 'done')->count();
                                                    $progression = $totalTaches > 0 ? round(($tachesTerminees / $totalTaches) * 100) : 0;

                                                    if ($progression < 25) {
                                                        $progressClass = 'bg-danger';
                                                    } elseif ($progression < 50) {
                                                        $progressClass = 'bg-warning';
                                                    } elseif ($progression < 75) {
                                                        $progressClass = 'bg-info';
                                                    } else {
                                                        $progressClass = 'bg-success';
                                                    }
                                                @endphp
                                                <div class="progress" style="height: 8px;">
                                                    <div class="progress-bar {{ $progressClass }} progress-bar-striped progress-bar-animated" role="progressbar" style="width: {{ $progression }}%;" aria-valuenow="{{ $progression }}" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                                <span class="text-muted mt-1 d-block small">{{ $progression }}% Complété</span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('projet-detail', ['id' => $project->id]) }}" class="btn btn-sm btn-soft-primary animate__animated animate__fadeIn" data-bs-toggle="tooltip" title="{{ __('message.View Details_list') }}">
                                                        <i class="ri-eye-fill"></i>
                                                    </a>
                                                    <a href="{{ route('projects.edit', $project->id) }}" class="btn btn-sm btn-soft-warning animate__animated animate__fadeIn" data-bs-toggle="tooltip" title="{{ __('message.Edit_list') }}">
                                                        <i class="ri-pencil-fill"></i>
                                                    </a>
                                                    <a href="javascript:void(0);" class="btn btn-sm btn-soft-danger delete-project-btn animate__animated animate__fadeIn" data-id="{{ $project->id }}" data-bs-toggle="tooltip" title="{{ __('message.Delete_list') }}">
                                                        <i class="ri-delete-bin-fill"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="6" class="text-center">
                                                <div class="py-4 animate__animated animate__fadeIn">
                                                    <i class="ri-folder-open-line display-5 text-muted animate__animated animate__pulse animate__infinite animate__slow"></i>
                                                    <h5 class="mt-2">{{ __('message.No project found_list') }}</h5>
                                                    <p class="text-muted">{{ __('message.Start by creating project_list') }}</p>
                                                    <a href="{{ route('projet') }}" class="btn btn-primary btn-lg animate__animated animate__heartBeat animate__infinite animate__slower">
                                                        <i class="ri-add-line align-bottom me-1"></i> {{ __('message.Create a Project_list') }}
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation pour la suppression -->
<div class="modal fade" id="deleteProjectModal" tabindex="-1" aria-labelledby="deleteProjectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteProjectModalLabel">{{ __('message.Delete Confirmation_list') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{{ __('message.Are you sure project_list') }}</p>
                <p class="text-danger"><small>{{ __('message.This action is irreversible_list') }}</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{ __('message.Cancel_list') }}</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">{{ __('message.Delete_list') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Formulaire caché pour la suppression -->
<form id="deleteProjectForm" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Variables pour le modal et le formulaire
        const deleteModalElement = document.getElementById('deleteProjectModal');
        const deleteForm = document.getElementById('deleteProjectForm');
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        let projectIdToDelete = null;

        // Initialiser la modal Bootstrap
        let deleteModal;
        if (deleteModalElement && typeof bootstrap !== 'undefined') {
            try {
                deleteModal = new bootstrap.Modal(deleteModalElement);
            } catch (e) {
                console.error("Erreur lors de l'initialisation de la modal:", e);
            }
        }

        // Gestion des boutons de suppression
        document.querySelectorAll('.delete-project-btn').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                projectIdToDelete = this.getAttribute('data-id');
                if (deleteModal) {
                    deleteModal.show();
                } else {
                    // Fallback si la modal ne fonctionne pas
                    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {
                        deleteForm.action = '{{ route("projects.delete", "") }}/' + projectIdToDelete;
                        deleteForm.submit();
                    }
                }
            });
        });

        // Action de confirmation de suppression
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', function() {
                if (projectIdToDelete) {
                    deleteForm.action = '{{ route("projects.delete", "") }}/' + projectIdToDelete;
                    deleteForm.submit();
                }
                if (deleteModal) {
                    deleteModal.hide();
                }
            });
        }

        // Initialiser les tooltips Bootstrap
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });

        // Animation des lignes du tableau au survol
        document.querySelectorAll('tbody tr').forEach(function(row) {
            row.addEventListener('mouseenter', function() {
                this.classList.add('animate__animated', 'animate__pulse');
                this.style.backgroundColor = '#f8f9fa';
                this.style.transition = 'all 0.3s ease';
            });
            row.addEventListener('mouseleave', function() {
                this.classList.remove('animate__animated', 'animate__pulse');
                this.style.backgroundColor = '';
            });
        });

        // Animation des boutons au survol
        document.querySelectorAll('.btn').forEach(function(btn) {
            if (!btn.classList.contains('animate__infinite')) {
                btn.addEventListener('mouseenter', function() {
                    this.classList.add('animate__animated', 'animate__pulse');
                });
                btn.addEventListener('mouseleave', function() {
                    this.classList.remove('animate__animated', 'animate__pulse');
                });
            }
        });
    });
</script>
@endsection

<style>
/* Styles personnalisés pour la liste des projets */
.table-hover tbody tr:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    z-index: 1;
    transition: all 0.3s ease;
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

.avatar-sm:hover {
    transform: scale(1.1);
    transition: all 0.3s ease;
}

.btn-group .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* Effet de survol sur les cartes */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* Styles pour les images de projet */
.project-thumbnail {
    transition: all 0.3s ease;
    border-radius: 8px !important;
}

.project-thumbnail:hover {
    transform: scale(1.1);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.project-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.project-image-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.project-image-container:hover::after {
    transform: translateX(100%);
}
</style>
