<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Tache;
use App\Models\Projet;

class EntreprisedashController extends Controller
{
   public function index(Request $request)
{
    $user = auth()->user(); // Récupère l'utilisateur connecté
    $projetId = $request->query('projet_id'); // ID projet spécifique

    // Récupérer les projets avec leurs tâches et personnels associés selon le rôle
    if ($user->role === 'entreprise') {
        $query = Projet::with('taches.personnel')
                    ->where('user_id', $user->id);
    } elseif (in_array($user->role, ['membre', 'client'])) {
        $query = Projet::with('taches.personnel')
                    ->where('user_id', $user->entreprise_id);
    } else {
        // Si autre rôle, on retourne aucun projet
        $query = Projet::with('taches.personnel')->whereNull('id');
    }

    // Si un ID de projet est fourni, filtrer pour ce projet spécifique
    if ($projetId) {
        $query->where('id', $projetId);
    }

    $projets = $query->get();

    // Récupérer les IDs des projets récupérés
    $projetIds = $projets->pluck('id')->toArray();

    // Filtrer les tâches par statut
    $to_do = Tache::whereIn('projet_id', $projetIds)->where('statut', 'to_do')->get();
    $doing = Tache::whereIn('projet_id', $projetIds)->where('statut', 'doing')->get();
    $bug = Tache::whereIn('projet_id', $projetIds)->where('statut', 'bug')->get();
    $done = Tache::whereIn('projet_id', $projetIds)->where('statut', 'done')->get();

    // Déterminer le thème en fonction du rôle de l'utilisateur
    $theme = \App\Helpers\ThemeHelper::getUserTheme();

    // Envoyer les données à la vue avec le thème approprié
    return view('dash-entreprise', compact('projets', 'to_do', 'doing', 'bug', 'done', 'projetId', 'theme'))
        ->with('userTheme', $theme);
}

}

