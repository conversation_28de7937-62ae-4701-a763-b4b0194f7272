<?php

namespace App\Http\Controllers;

use App\Models\projet;
use App\Models\User;
use Illuminate\Http\Request;
use App\Helpers\ImageHelper;
use Illuminate\Support\Facades\Storage;

class ProjetController extends Controller
{
    // Afficher le formulaire pour ajouter un projet
    public function index()
    {
        // Récupérer les membres avec le rôle 'membre' ajoutés par l'utilisateur connecté
        $membres = User::where('role', 'membre')
                      ->where('entreprise_id', auth()->id())
                      ->get();

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('projet', compact('membres'))->with('userTheme', $theme);
    }

    // Enregistrer un nouveau projet
    public function store(Request $request)
    {
        // Validation des données de la requête
        $validated = $request->validate([
            'project_name' => 'required|string|max:255',
            'task_title' => 'required|string|max:255',
            'task_description' => 'nullable|string|max:2000',
            'task_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // Max 5MB
            'members' => 'nullable|array',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
        ]);

        // Traitement de l'image si elle est présente
        $imagePath = null;
        if ($request->hasFile('task_image')) {
            try {
                $uploadedFiles = ImageHelper::uploadImage(
                    $request->file('task_image'),
                    'projects',
                    ['original', 'thumbnail', 'medium']
                );
                $imagePath = $uploadedFiles['original'];
            } catch (\Exception $e) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['task_image' => 'Erreur lors de l\'upload de l\'image: ' . $e->getMessage()]);
            }
        }

        // Création du projet avec l'ID de l'utilisateur connecté
        $project = projet::create([
            'project_name' => $validated['project_name'],
            'task_title' => $validated['task_title'],
            'task_description' => $validated['task_description'],
            'task_image' => $imagePath,
            'start_date' => $validated['start_date'],
            'end_date' => $validated['end_date'],
            'members' => json_encode($request->members ?? []),
            'user_id' => auth()->id(), // Assigne l'ID de l'utilisateur connecté
        ]);

        // Redirection après la création du projet
        return redirect()->route('projects.list')
            ->with('success', 'Projet créé avec succès!');
    }

    // Afficher la liste des projets
    public function list()
    {
        // Si l'utilisateur est une entreprise, montrer seulement ses projets
        if (auth()->user()->role == 'entreprise') {
            $projects = projet::where('user_id', auth()->id())->get();
        } else {
            // Pour les admins ou autres rôles, montrer tous les projets
            $projects = projet::all();
        }
        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('liste-projet', compact('projects'))->with('userTheme', $theme);
    }

    // Afficher le formulaire pour modifier un projet
    public function edit($id)
    {
        $project = projet::findOrFail($id);

        // Vérifier si l'utilisateur est autorisé à modifier ce projet
        // Si c'est une entreprise, elle ne peut modifier que ses propres projets
        if (auth()->user()->role == 'entreprise' && $project->user_id != auth()->id()) {
            return redirect()->route('projects.list')
                ->with('error', 'Vous n\'êtes pas autorisé à modifier ce projet.');
        }

        // Récupérer les membres avec le rôle 'membre' ajoutés par l'utilisateur connecté
        $personnels = User::where('role', 'membre')
                        ->where('entreprise_id', auth()->id())
                        ->get();

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('modifier-projet', compact('project', 'personnels'))->with('userTheme', $theme);
    }

    // Mettre à jour un projet existant
    public function update(Request $request, $id)
    {
        $project = projet::findOrFail($id);

        // Vérifier si l'utilisateur est autorisé à modifier ce projet
        // Si c'est une entreprise, elle ne peut modifier que ses propres projets
        if (auth()->user()->role == 'entreprise' && $project->user_id != auth()->id()) {
            return redirect()->route('projects.list')
                ->with('error', 'Vous n\'êtes pas autorisé à modifier ce projet.');
        }

        $validated = $request->validate([
            'project_name' => 'required|string|max:255',
            'task_title' => 'required|string|max:255',
            'task_description' => 'nullable|string|max:2000',
            'task_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after:start_date',
        ]);

        // Traitement de l'image si elle est présente
        if ($request->hasFile('task_image')) {
            try {
                // Supprimer l'ancienne image si elle existe
                if ($project->task_image) {
                    ImageHelper::deleteImage($project->task_image);
                }

                // Upload de la nouvelle image
                $uploadedFiles = ImageHelper::uploadImage(
                    $request->file('task_image'),
                    'projects',
                    ['original', 'thumbnail', 'medium']
                );
                $project->task_image = $uploadedFiles['original'];
            } catch (\Exception $e) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['task_image' => 'Erreur lors de l\'upload de l\'image: ' . $e->getMessage()]);
            }
        }

        // Mise à jour du projet
        $project->update([
            'project_name' => $validated['project_name'],
            'task_title' => $validated['task_title'],
            'task_description' => $validated['task_description'],
            'start_date' => $validated['start_date'],
            'end_date' => $validated['end_date'],
        ]);

        // Redirection après la mise à jour
        return redirect()->route('projects.list')->with('success', 'Projet mis à jour avec succès.');
    }

    // Supprimer un projet
    public function destroy($id)
    {
        $project = projet::findOrFail($id);

        // Vérifier si l'utilisateur est autorisé à supprimer ce projet
        // Si c'est une entreprise, elle ne peut supprimer que ses propres projets
        if (auth()->user()->role == 'entreprise' && $project->user_id != auth()->id()) {
            return redirect()->route('projects.list')
                ->with('error', 'Vous n\'êtes pas autorisé à supprimer ce projet.');
        }

        // Supprimer l'image associée si elle existe
        if ($project->task_image) {
            ImageHelper::deleteImage($project->task_image);
        }

        $project->delete();
        return redirect()->route('projects.list')->with('success', 'Projet supprimé avec succès.');
    }
}

