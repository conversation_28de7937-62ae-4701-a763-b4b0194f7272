@extends($userTheme ?? 'theme')
@section('contenu')

```
                <!-- start page title -->
                <div class="row">
                    <div class="col-12">
                        <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
                            <h4 class="mb-sm-0">{{ __('message.Add Project_add') }}</h4>
                            <div class="page-title-right">
                                <ol class="breadcrumb m-0">
                                    <li class="breadcrumb-item"><a href="javascript: void(0);">TaskFlow</a></li>
                                    <li class="breadcrumb-item active">{{ __('message.Projects_list') }}</li>
                                </ol>
                            </div>

                        </div>
                    </div>
                </div>
                <!-- end page title -->

                <section class="card p-4 mt-4">
<h5 class="mb-3">{{ __('message.Add Project_add') }}</h5>
@if ($errors->any())
    <div class="alert alert-danger">
        <ul class="mb-0">
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
<form action="{{ route('projects.store') }}" method="POST" enctype="multipart/form-data">
@csrf
<!-- Champ Project Name -->
<div class="col-lg-12">
    <label for="projectName" class="form-label">{{ __('message.Project Name_add') }}</label>
    <input type="text" class="form-control" id="projectName" name="project_name" placeholder="Enter project name" required>
</div>

<!-- Champ Task Title -->
<div class="col-lg-12">
    <label for="sub-tasks" class="form-label">{{ __('message.Task Title_add') }}</label>
    <input type="text" class="form-control" id="sub-tasks" name="task_title" placeholder="Task title" required>
</div>

<!-- Champ Task Description -->
<div class="col-lg-12">
    <label for="task-description" class="form-label">{{ __('message.Task Description_add') }}</label>
    <textarea class="form-control" id="task-description" name="task_description" rows="3" placeholder="Task description"></textarea>
</div>

<!-- Champ Task Image -->
<div class="col-lg-12">
    <label for="task_image" class="form-label">{{ __('message.Image_add') }} ({{ __('message.Optional_add') }})</label>
    <div class="image-upload-container">
        <input class="form-control" type="file" id="task_image" name="task_image" accept="image/*" onchange="previewImage(this)">
        <div class="form-text text-muted">
            <i class="ri-information-line me-1"></i>
            Formats acceptés: JPG, PNG, GIF, WEBP. Taille max: 5MB
        </div>

        <!-- Prévisualisation de l'image -->
        <div id="image-preview" class="mt-3" style="display: none;">
            <div class="card" style="max-width: 300px;">
                <img id="preview-img" src="" class="card-img-top" style="height: 200px; object-fit: cover;">
                <div class="card-body p-2">
                    <small class="text-muted">Aperçu de l'image</small>
                    <button type="button" class="btn btn-sm btn-outline-danger float-end" onclick="removePreview()">
                        <i class="ri-delete-bin-line"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function previewImage(input) {
    const preview = document.getElementById('image-preview');
    const previewImg = document.getElementById('preview-img');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'block';
        }

        reader.readAsDataURL(input.files[0]);
    } else {
        preview.style.display = 'none';
    }
}

function removePreview() {
    document.getElementById('task_image').value = '';
    document.getElementById('image-preview').style.display = 'none';
}
</script>

<!-- Membres -->
<div class="col-lg-12">
    <label class="form-label">Add Team Members</label>
    <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
        <ul class="list-unstyled vstack gap-2 mb-0">
            @if(isset($membres) && count($membres) > 0)
                @foreach($membres as $membre)
                <li>
                    <div class="form-check d-flex align-items-center">
                        <input class="form-check-input me-3" type="checkbox" name="members[]" value="{{ $membre->id }}" id="membre-{{ $membre->id }}">
                        <label class="form-check-label d-flex align-items-center" for="membre-{{ $membre->id }}">
                            <span class="flex-shrink-0">
                                {!! $membre->getAvatarHtml('xs', 'me-2') !!}
                            </span>
                            <span class="flex-grow-1 ms-2">{{ $membre->name }}</span>
                        </label>
                    </div>
                </li>
                @endforeach
            @else
                <li>Aucun membre disponible pour cette entreprise</li>
            @endif
        </ul>
    </div>
</div>

<!-- Date de début et de fin -->
<div class="col-lg-4">
    <label for="start-date" class="form-label">{{ __('message.Start Date_add') }}</label>
    <input type="date" class="form-control" id="start-date" name="start_date" min="{{ date('Y-m-d') }}" required>
</div>

<div class="col-lg-4">
    <label for="end-date" class="form-label">{{ __('message.End Date_add') }}</label>
    <input type="date" class="form-control" id="end-date" name="end_date">
</div>

<!-- Progress -->


<div class="col-12 mt-4 text-end">
    <button type="submit" class="btn btn-success">{{ __('message.Add_project') }}</button>
</div>

</form>

</section>

                <!--end row-->
                @endsection


