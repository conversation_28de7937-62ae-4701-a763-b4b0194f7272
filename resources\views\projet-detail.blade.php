@extends($userTheme ?? 'theme')

@section('contenu')
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
                    <h4 class="mb-sm-0">{{ __('message.Project Details') }}</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="/liste-projet">{{ __('message.Projects_detail') }}</a></li>
                            <li class="breadcrumb-item active">{{ __('message.Details_detail') }}</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <!-- end page title -->

        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <div class="row">
            <!-- Informations du projet -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header bg-soft-primary">
                        <h5 class="card-title mb-0">{{ __('message.Project Information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h4>{{ $projet->project_name }}</h4>
                            @if($projet->task_image)
                                <div class="mt-3 mb-3">
                                    <img src="{{ asset('storage/' . $projet->task_image) }}" alt="Image du projet" class="img-fluid rounded" style="max-height: 200px;">
                                </div>
                            @endif
                        </div>

                        <div class="mb-3">
                            <h5>{{ __('message.Main Task Title') }}</h5>
                            <p>{{ $projet->task_title }}</p>
                        </div>

                        @if($projet->task_description)
                            <div class="mb-3">
                                <h5>{{ __('message.Description_detail') }}</h5>
                                <p>{{ $projet->task_description }}</p>
                            </div>
                        @endif

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h5>{{ __('message.Start Date_detail') }}</h5>
                                <p>{{ \Carbon\Carbon::parse($projet->start_date)->format('d/m/Y') }}</p>
                            </div>
                            <div class="col-md-6">
                                <h5>{{ __('message.End Date_detail') }}</h5>
                                <p>{{ $projet->end_date ? \Carbon\Carbon::parse($projet->end_date)->format('d/m/Y') : __('message.Not defined') }}</p>
                            </div>
                        </div>

                        @if($projet->members)
                            <div class="mb-3">
                                <h5>{{ __('message.Members') }}</h5>
                                <p>{{ $projet->members }}</p>
                            </div>
                        @endif

                        <div class="mb-3">
                            <h5>{{ __('message.Progress_detail') }}</h5>
                            @php
                                $totalTaches = $projet->taches->count();
                                $tachesTerminees = $projet->taches->where('statut', 'done')->count();
                                $progression = $totalTaches > 0 ? round(($tachesTerminees / $totalTaches) * 100) : 0;
                            @endphp
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: {{ $progression }}%;"
                                    aria-valuenow="{{ $progression }}" aria-valuemin="0" aria-valuemax="100">
                                    {{ $progression }}%
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="/projet/edit/{{ $projet->id }}" class="btn btn-warning">
                                <i class="ri-edit-2-line align-bottom me-1"></i> {{ __('message.Edit_detail') }}
                            </a>
                            <a href="/liste-projet" class="btn btn-secondary">
                                <i class="ri-arrow-left-line align-bottom me-1"></i> {{ __('message.Back_detail') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liste des tâches -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-soft-primary d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">{{ __('message.Project Tasks') }}</h5>
                        <a href="/ajout-tache?projet_id={{ $projet->id }}" class="btn btn-sm btn-primary">
                            <i class="ri-add-line align-bottom me-1"></i> {{ __('message.New Task_detail') }}
                        </a>
                    </div>
                    <div class="card-body">
                        @if($projet->taches->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>{{ __('message.Name_detail') }}</th>
                                            <th>{{ __('message.Deadline_detail') }}</th>
                                            <th>{{ __('message.Status_detail') }}</th>
                                            <th>{{ __('message.Progress_detail2') }}</th>
                                            <th>{{ __('message.Assigned to_detail') }}</th>
                                            <th>{{ __('message.Actions_detail') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($projet->taches as $tache)
                                        <tr>
                                            <td>{{ $tache->nom }}</td>
                                            <td>{{ \Carbon\Carbon::parse($tache->deadline)->format('d/m/Y') }}</td>
                                            <td>
                                                @if($tache->statut == 'to_do')
                                                    <span class="badge bg-secondary">{{ __('message.To do_detail') }}</span>
                                                @elseif($tache->statut == 'doing')
                                                    <span class="badge bg-warning">{{ __('message.In progress_detail') }}</span>
                                                @elseif($tache->statut == 'bug')
                                                    <span class="badge bg-danger">{{ __('message.Bugs') }}</span>
                                                @elseif($tache->statut == 'done')
                                                    <span class="badge bg-success">{{ __('message.Completed_detail') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-primary" role="progressbar" style="width: {{ $tache->avancement }}%;"
                                                        aria-valuenow="{{ $tache->avancement }}" aria-valuemin="0" aria-valuemax="100">
                                                        {{ $tache->avancement }}%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                @if($tache->personnel)
                                                    {{ $tache->personnel->name }}
                                                @else
                                                    <span class="text-muted">{{ __('message.Not assigned_detail') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('tache-detail', ['id' => $tache->id]) }}" class="btn btn-sm btn-info">
                                                        <i class="ri-eye-fill"></i>
                                                    </a>
                                                    <a href="/modifier-tache/{{ $tache->id }}" class="btn btn-sm btn-warning">
                                                        <i class="ri-edit-2-line"></i>
                                                    </a>
                                                    <a href="/supprimer-tache/{{ $tache->id }}" class="btn btn-sm btn-danger"
                                                       onclick="return confirm('{{ __('message.Are you sure to delete') }}')">
                                                        <i class="ri-delete-bin-5-line"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info">
                                {{ __('message.No task associated') }}
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Statistiques des tâches -->
                <div class="card mt-4">
                    <div class="card-header bg-soft-primary">
                        <h5 class="card-title mb-0">{{ __('message.Task Statistics') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light mb-3">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">{{ __('message.Total tasks') }}</h5>
                                        <h2 class="mt-3">{{ $projet->taches->count() }}</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light mb-3">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">{{ __('message.Completed tasks') }}</h5>
                                        <h2 class="mt-3">{{ $projet->taches->where('statut', 'done')->count() }}</h2>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-3">
                                <div class="card bg-secondary-subtle mb-3">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">{{ __('message.To do_detail') }}</h6>
                                        <h3 class="mt-2">{{ $projet->taches->where('statut', 'to_do')->count() }}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning-subtle mb-3">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">{{ __('message.In progress_detail') }}</h6>
                                        <h3 class="mt-2">{{ $projet->taches->where('statut', 'doing')->count() }}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger-subtle mb-3">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">{{ __('message.Bugs') }}</h6>
                                        <h3 class="mt-2">{{ $projet->taches->where('statut', 'bug')->count() }}</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success-subtle mb-3">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">{{ __('message.Completed_detail') }}</h6>
                                        <h3 class="mt-2">{{ $projet->taches->where('statut', 'done')->count() }}</h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
@endsection