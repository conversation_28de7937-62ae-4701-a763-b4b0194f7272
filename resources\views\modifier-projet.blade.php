@extends($userTheme ?? 'theme')

@section('contenu')

<div class="container mt-4">
    <h2>{{ __('message.Edit Project') }}</h2>

<form action="{{ route('projects.update', $project->id) }}" method="POST" enctype="multipart/form-data">
    @csrf
    @method('PUT')

    <div class="mb-3">
        <label>{{ __('message.Project Name_edit') }}</label>
        <input type="text" name="project_name" class="form-control" value="{{ old('project_name', $project->project_name) }}" required>
    </div>

    <div class="mb-3">
        <label>{{ __('message.Task Title_edit') }}</label>
        <input type="text" name="task_title" class="form-control" value="{{ old('task_title', $project->task_title) }}" required>
    </div>

    <div class="mb-3">
        <label>{{ __('message.Task Description_edit') }}</label>
        <textarea name="task_description" class="form-control">{{ old('task_description', $project->task_description) }}</textarea>
    </div>

    <div class="mb-3">
        <label>{{ __('message.Image_edit') }} ({{ __('message.Optional_edit') }})</label>
        <input type="file" name="task_image" class="form-control">
        @if($project->task_image)
            <img src="{{ asset('storage/' . $project->task_image) }}" width="100" class="mt-2">
        @endif
    </div>

    <div class="mb-3">
        <label>{{ __('message.Start Date_edit') }}</label>
        <input type="date" name="start_date" class="form-control" value="{{ old('start_date', $project->start_date) }}" required>
    </div>

    <div class="mb-3">
        <label>{{ __('message.End Date_edit') }}</label>
        <input type="date" name="end_date" class="form-control" value="{{ old('end_date', $project->end_date) }}">
    </div>

    <!-- Membres -->
    <div class="col-lg-12">
        <label class="form-label">Add Team Members</label>
        <div class="border rounded p-2" style="max-height: 150px; overflow-y: auto;">
            <ul class="list-unstyled vstack gap-2 mb-0">
                @if(isset($personnels) && count($personnels) > 0)
                    @foreach($personnels as $personnel)
                    <li>
                        <div class="form-check d-flex align-items-center">
                            <input class="form-check-input me-3" type="checkbox" name="members[]"
                                   value="{{ $personnel->id }}" id="personnel-{{ $personnel->id }}"
                                   @if(isset($project) && $project->members && in_array($personnel->id, json_decode($project->members))) checked @endif>
                            <label class="form-check-label d-flex align-items-center" for="personnel-{{ $personnel->id }}">
                                <span class="flex-shrink-0">
                                    <img src="{{ asset('assets/images/users/avatar-1.jpg') }}" alt="" class="avatar-xxs rounded-circle" />
                                </span>
                                <span class="flex-grow-1 ms-2">{{ $personnel->name }}</span>
                            </label>
                        </div>
                    </li>
                    @endforeach
                @else
                    <li>Aucun membre disponible pour cette entreprise</li>
                @endif
            </ul>
        </div>
    </div>

    <button type="submit" class="btn btn-primary">{{ __('message.Update Project') }}</button>
    <a href="{{ route('projects.list') }}" class="btn btn-secondary">{{ __('message.Cancel') }}</a>
</form>

</div>

@endsection
