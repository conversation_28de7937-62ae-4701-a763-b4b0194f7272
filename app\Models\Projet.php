<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Projet extends Model
{
    use HasFactory;

    protected $table = 'projets';
    
    protected $fillable = [
        'project_name',
        'task_title',
        'task_description',
        'task_image',
        'members',
        'start_date',
        'end_date',
        'user_id'
    ];

    /**
     * Obtenir l'URL de l'image du projet
     *
     * @param string $size
     * @return string
     */
    public function getImageUrl($size = 'original')
    {
        return \App\Helpers\ImageHelper::getImageUrl(
            $this->task_image,
            $size,
            asset('assets/images/defaults/project-placeholder.svg')
        );
    }

    /**
     * Vérifier si le projet a une image
     *
     * @return bool
     */
    public function hasImage()
    {
        return !empty($this->task_image) && \Illuminate\Support\Facades\Storage::disk('public')->exists($this->task_image);
    }

    /**
     * Obtenir les informations de l'image
     *
     * @return array|null
     */
    public function getImageInfo()
    {
        return \App\Helpers\ImageHelper::getImageInfo($this->task_image);
    }

    /**
     * Obtenir l'image avec fallback
     *
     * @param string $size
     * @return string
     */
    public function getImageOrPlaceholder($size = 'medium')
    {
        if ($this->hasImage()) {
            return $this->getImageUrl($size);
        }

        return \App\Helpers\ImageHelper::createPlaceholder(
            400, 300,
            $this->project_name,
            '#f8f9fa',
            '#6c757d'
        );
    }

    // Relation avec l'utilisateur qui a créé le projet
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    // Relation avec les membres du projet (si vous utilisez une table pivot)
    public function members()
    {
        return $this->belongsToMany(User::class, 'project_personnel', 'projet_id', 'personnel_id');
    }

    /**
     * Relation : Un projet a plusieurs tâches.
     */
    public function taches()
    {
        return $this->hasMany(Tache::class, 'projet_id');
    }
}


